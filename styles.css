/* styles.css */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    background-color: #fff;
    color: #333;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 50px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
}

.logo img {
    height: 40px;
}

.nav-links {
    list-style: none;
    display: flex;
    gap: 20px;
    margin: 0;
    padding: 0;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
}

.nav-links a:hover {
    color: #007bff;
}

.login-btn {
    background-color: #007bff;
    color: #fff;
    padding: 10px 15px;
    border-radius: 5px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.hero-section {
    display: flex;
    justify-content: space-between;
    padding: 50px;
    background-color: #f8f9fa;
}

.hero-content {
    max-width: 50%;
}

.hero-content h1 {
    font-size: 2.5rem;
    color: #002366;
}

.hero-content p {
    font-size: 1.1rem;
    line-height: 1.6;
}

.hero-content p strong {
    color: #007bff;
    font-weight: bold;
}

.stats-box {
    background-color: #e9f5ff;
    border-left: 4px solid #007bff;
    padding: 20px;
    margin-top: 20px;
    border-radius: 5px;
}

.stats-box p {
    margin: 10px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.hero-video {
    width: 40%;
}

.video-placeholder {
    background-color: #007bff;
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    position: relative;
    height: 250px;
}

.video-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.video-header span {
    font-size: 1.5rem;
    font-weight: bold;
}

.video-header img {
    width: 40px;
}

.video-body p {
    font-size: 1.2rem;
}

.video-person {
    position: absolute;
    bottom: 0;
    right: 20px;
    height: 200px;
}

.demo-btn {
    background-color: #ffc107;
    color: #333;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    position: absolute;
    bottom: 20px;
    left: 20px;
}

.programs-section {
    padding: 50px;
    text-align: center;
}

.programs-section h2 {
    font-size: 2rem;
    margin-bottom: 10px;
}

.underline {
    width: 100px;
    height: 4px;
    background-color: #007bff;
    margin: 0 auto 30px;
    border-radius: 2px;
}

.program-tabs {
    margin-bottom: 30px;
}

.tab-btn {
    background-color: #fff;
    border: 1px solid #007bff;
    color: #007bff;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    margin: 0 5px;
}

.tab-btn.active {
    background-color: #007bff;
    color: #fff;
}

.program-cards {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.card {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 20px;
    width: 300px;
    text-align: left;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-icons {
    text-align: center;
    margin-bottom: 15px;
}

.card h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.card ul {
    list-style: disc;
    padding-left: 20px;
    color: #555;
}

.card ul li {
    margin-bottom: 10px;
}

.know-more-btn {
    display: block;
    background-color: #00bcd4;
    color: #fff;
    padding: 10px;
    border-radius: 5px;
    text-decoration: none;
    text-align: center;
    margin-top: 20px;
}

.explore-btn {
    background-color: #007bff;
    color: #fff;
    padding: 15px 30px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    margin-top: 40px;
    display: inline-block;
}

.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-widget span {
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.chat-icon {
    background-color: #ef4b6c;
    color: #fff;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    cursor: pointer;
}
