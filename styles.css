/* General Setup and Variables */
:root {
    --primary-blue: #007BFF;
    --dark-blue: #1e2a4a;
    --light-blue: #e6f2ff;
    --accent-green: #c5e021;
    --text-dark: #333;
    --text-light: #666;
    --bg-white: #fff;
    --border-color: #ddd;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    color: var(--text-dark);
    background-color: var(--bg-white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: inherit;
}

/* ================================== */
/* CORRECTED HEADER CSS - START     */
/* ================================== */

/* General Header Styling */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.07);
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

header nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Logo Styling */
.logo a {
    display: flex;
    flex-direction: column;
}

.logo strong {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-blue);
    line-height: 1.1;
}

.logo span {
    font-size: 12px;
    color: var(--text-light);
}

/* Navigation Links */
.nav-links {
    display: flex;
    align-items: center;
    list-style: none;
    gap: 25px;
}

.nav-links a {
    font-size: 15px;
    font-weight: 500;
    color: var(--text-dark);
    transition: color 0.3s;
}

.nav-links a:hover {
    color: var(--primary-blue);
}

.nav-links i.fa-chevron-down {
    font-size: 10px;
    margin-left: 5px;
}

/* Auth Button - CORRECTED */
.btn-login {
    background-color: var(--primary-blue); /* Solid blue background */
    color: var(--bg-white);                /* White text */
    border: none;                          /* No border */
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 15px;
    display: flex;
    align-items: center;
    gap: 8px; /* Space between icon and text */
    transition: background-color 0.3s ease;
}

.btn-login:hover {
    background-color: #0056b3; /* A darker blue for hover effect */
}


/* ================================== */
/* CORRECTED HEADER CSS - END      */
/* ================================== */

/* Hero Section */
.hero-section {
    padding: 80px 0;
}

.hero-content {
    display: flex;
    align-items: center;
    gap: 60px;
}

.hero-left {
    flex: 1;
}

.hero-left h1 {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
}

.hero-left p {
    font-size: 16px;
    color: var(--text-light);
    margin-bottom: 15px;
    line-height: 1.6;
}

.hero-left p strong {
    color: var(--primary-blue);
    font-weight: 600;
}

.stats-box {
    background-color: var(--dark-blue);
    color: var(--bg-white);
    padding: 25px;
    border-radius: 10px;
    margin-top: 30px;
}

.stats-box p {
    color: var(--bg-white);
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 15px;
}

.stats-box p:not(:last-child) {
    margin-bottom: 15px;
}

.stats-box i {
    color: var(--accent-green);
    font-size: 20px;
    width: 20px;
    text-align: center;
}

.hero-right {
    flex: 0 0 450px; /* Don't grow, don't shrink, base width 450px */
}

.video-card {
    background-color: var(--bg-white);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.video-card .video-text h3 {
    font-size: 24px;
    margin-bottom: 5px;
}

.video-card .video-text p {
    font-size: 16px;
    color: var(--text-light);
}

.person-image {
    width: 250px;
    position: relative;
    bottom: -6px; /* Adjust to sit perfectly on the button */
}

.play-icon-area {
    position: absolute;
    top: 60px;
    left: 20px;
    background-color: #d1eaff;
    color: var(--primary-blue);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: grid;
    place-items: center;
}

.btn-demo {
    display: block;
    background-color: var(--accent-green);
    color: var(--text-dark);
    font-weight: 600;
    padding: 15px;
    border-radius: 8px;
    transition: background-color 0.3s;
}

.btn-demo:hover {
    background-color: #b0c71e;
}
.btn-demo i {
    margin-left: 8px;
}

/* Programs Section */
.programs-section {
    padding: 60px 0;
    text-align: center;
}

.section-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

/* Scribble underline effect */
.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 150px;
    height: 10px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 12"><path d="M_5_5_Q_20_10_40_5_T_95_5" fill="none" stroke="%23007BFF" stroke-width="2" stroke-linecap="round" /></svg>');
    background-repeat: no-repeat;
}

.tabs {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 40px;
}

.tab-button {
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 500;
    border: 1px solid var(--primary-blue);
    border-radius: 30px;
    background-color: var(--bg-white);
    color: var(--primary-blue);
    cursor: pointer;
    transition: all 0.3s;
}

.tab-button:hover {
    background-color: var(--light-blue);
}

.tab-button.active {
    background-color: var(--primary-blue);
    color: var(--bg-white);
}

/* Chat Widget */
.chat-widget {
    position: fixed;
    bottom: 25px;
    right: 25px;
    background-color: var(--bg-white);
    box-shadow: 0 5px 15px rgba(0,0,0,0.15);
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 10px 15px;
    cursor: pointer;
}

.chat-widget span {
    font-size: 14px;
    font-weight: 500;
    margin-right: 15px;
}

.chat-icon {
    background-color: #ef5350;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: grid;
    place-items: center;
    font-size: 20px;
}