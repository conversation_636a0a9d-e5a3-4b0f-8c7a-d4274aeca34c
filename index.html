<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScholarHat Clone</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
</head>
<body>

    <header>
        <nav class="container">
            <div class="logo">
                <a href="#">
                    <strong>ScholarHat</strong>
                    <span>By DotNetTricks</span>
                </a>
            </div>
            <ul class="nav-links">
                <li><a href="#">Live Training <i class="fa-solid fa-chevron-down"></i></a></li>
                <li><a href="#">Job Oriented Training <i class="fa-solid fa-chevron-down"></i></a></li>
                <li><a href="#">Free Master Classes</a></li>
                <li><a href="#">Free Courses</a></li>
                <li><a href="#">Free Books</a></li>
                <li><a href="#">Training Batches</a></li>
                <li><a href="#">Explore <i class="fa-solid fa-chevron-down"></i></a></li>
                <li><a href="#">Tutorials</a></li>
            </ul>
            <div class="auth-buttons">
                <a href="#" class="btn-login">Login/SignUp</a>
            </div>
        </nav>
    </header>

    <main>
        <section class="hero-section">
            <div class="container hero-content">
                <div class="hero-left">
                    <h1>Build a High-Paying AI-Ready Tech Career</h1>
                    <p>Learn live from Microsoft MVPs and Software Architect to master in demand job skills.</p>
                    <p><strong>Build</strong> real-world projects with recommended design patterns and best practices.</p>
                    <p><strong>Empower</strong> yourself to crack your next job interviews at world top tech companies.</p>

                    <div class="stats-box">
                        <p><i class="fa-solid fa-briefcase"></i> 93% got jobs within 6 months of training program.</p>
                        <p><i class="fa-solid fa-user-tie"></i> Experienced developers got upto 35 LPA Salary.</p>
                        <p><i class="fa-solid fa-user-tie"></i> Experienced software architect got upto 55 LPA Salary.</p>
                        <p><i class="fa-solid fa-user-graduate"></i> Beginners/Freshers got upto 9 LPA Salary.</p>
                    </div>
                </div>
                <div class="hero-right">
                    <div class="video-card">
                         <div class="video-text">
                            <h3>What is ScholarHat ?</h3>
                            <p>An EdTech Platform</p>
                         </div>
                         <img src="https://i.ibb.co/L5w2sW8/scholarhat-person.png" alt="Man in a suit" class="person-image">
                         <div class="play-icon-area">
                             <i class="fa-solid fa-play"></i>
                         </div>
                         <a href="#" class="btn-demo">Book A Free Demo <i class="fa-solid fa-arrow-right"></i></a>
                    </div>
                </div>
            </div>
        </section>

        <section class="programs-section">
             <div class="container">
                <h2 class="section-title">Job Oriented Programs</h2>
                <div class="tabs">
                    <button class="tab-button active">For Beginners</button>
                    <button class="tab-button">For Experienced</button>
                </div>
                </div>
        </section>
    </main>

    <div class="chat-widget">
        <span>I'm here to help you!</span>
        <div class="chat-icon">
            <i class="fa-solid fa-comment-dots"></i>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>