document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.tab-button');

    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove 'active' class from all tabs
            tabs.forEach(item => item.classList.remove('active'));
            
            // Add 'active' class to the clicked tab
            tab.classList.add('active');

            // In a real application, you would also add logic here to
            // show the content corresponding to the clicked tab.
            // For example:
            // const tabContentId = tab.dataset.tab;
            // showContent(tabContentId);
        });
    });
});